{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "cli": {"analytics": false, "schematicCollections": ["angular-eslint"]}, "projects": {"desktop": {"projectType": "application", "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular/build:application", "options": {"outputPath": "dist", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "styles": ["@angular/material/prebuilt-themes/azure-blue.css", "src/styles.scss"], "assets": ["src/assets"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "10kb", "maximumError": "20kb"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular/build:dev-server", "options": {"port": 1420}, "configurations": {"production": {"buildTarget": "desktop:build:production"}, "development": {"buildTarget": "desktop:build:development"}}, "defaultConfiguration": "development"}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}}