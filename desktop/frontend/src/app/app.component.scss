@use "@angular/material" as mat;

// App Container
.app-container {
  height: 100vh;
  width: 100vw;
}

// Sidebar Styles
.app-sidenav {
  width: 280px;
  background: #e3f2fd;
  border-right: 1px solid #e0e0e0;
}

.sidenav-header {
  padding: 24px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  border-bottom: 1px solid #e0e0e0;
  background: linear-gradient(135deg, #1976d2, #00acc1);
  color: white;

  .app-icon {
    font-size: 32px;
    width: 32px;
    height: 32px;
  }

  .app-title {
    margin: 0;
    font-size: 20px;
    font-weight: 500;
    letter-spacing: 0.5px;
  }
}

.nav-list {
  padding-top: 8px;

  .nav-item {
    margin: 4px 8px;
    border-radius: 12px;
    transition: all 0.2s ease-in-out;
    cursor: pointer;

    &:hover {
      background-color: #bbdefb;
    }

    &.active {
      background-color: #90caf9;
      color: #1565c0;

      mat-icon {
        color: #1976d2;
      }
    }

    mat-icon {
      color: #757575;
    }
  }
}

// Main Content Styles
.main-content {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.app-toolbar {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 10;

  .toolbar-title {
    font-size: 18px;
    font-weight: 500;
    margin-left: 16px;
  }

  .toolbar-spacer {
    flex: 1 1 auto;
  }

  .menu-button {
    margin-right: 8px;
  }

  .toolbar-actions {
    display: flex;
    gap: 8px;
  }
}

.page-content {
  flex: 1;
  overflow: auto;
  padding: 24px;
  background-color: #fafafa;
}

// Responsive Design
@media (max-width: 768px) {
  .app-sidenav {
    width: 100vw;
  }

  .page-content {
    padding: 16px;
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .app-sidenav {
    background: #212121;
    border-right-color: #424242;
  }

  .sidenav-header {
    border-bottom-color: #424242;
  }

  .nav-list .nav-item {
    &:hover {
      background-color: #424242;
    }

    &.active {
      background-color: #0d47a1;
      color: #90caf9;
    }
  }

  .page-content {
    background-color: #121212;
  }
}
