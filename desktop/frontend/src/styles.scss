// Import Angular Material prebuilt theme
@import "@angular/material/prebuilt-themes/indigo-pink.css";

// Tailwind CSS
@tailwind base;
@tailwind components;
@tailwind utilities;

// Global styles
* {
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  margin: 0;
  font-family: "Roboto", "Helvetica Neue", sans-serif;
}

// Custom Material Design overrides
.mat-mdc-card {
  border-radius: 16px !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
}

.mat-mdc-button {
  border-radius: 20px !important;
}

.mat-mdc-fab {
  border-radius: 16px !important;
}

// Dark theme support will be handled by Material Design 3

html,
body {
  height: 100%;
}
body {
  margin: 0;
  font-family: Roboto, "Helvetica Neue", sans-serif;
}
