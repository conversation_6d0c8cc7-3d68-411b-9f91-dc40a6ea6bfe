// Angular Material Theme
@use "@angular/material" as mat;

// Include the common styles for Angular Material
@include mat.core();

// Define Material Design 3 color palette
$primary-palette: mat.define-palette(mat.$blue-palette, 600, 100, 900);
$accent-palette: mat.define-palette(mat.$teal-palette, 500, 200, 800);
$warn-palette: mat.define-palette(mat.$red-palette, 500, 300, 800);

// Create Material Design 3 theme
$theme: mat.define-light-theme(
  (
    color: (
      primary: $primary-palette,
      accent: $accent-palette,
      warn: $warn-palette,
    ),
    typography:
      mat.define-typography-config(
        $font-family: '"Roboto", "Helvetica Neue", sans-serif',
        $headline-1:
          mat.define-typography-level(
            112px,
            112px,
            300,
            $letter-spacing: -0.05em
          ),
        $headline-2:
          mat.define-typography-level(56px, 56px, 400, $letter-spacing: -0.02em),
        $headline-3:
          mat.define-typography-level(
            45px,
            48px,
            400,
            $letter-spacing: -0.005em
          ),
        $headline-4: mat.define-typography-level(34px, 40px, 400),
        $headline-5: mat.define-typography-level(24px, 32px, 400),
        $headline-6: mat.define-typography-level(20px, 32px, 500),
        $subtitle-1: mat.define-typography-level(16px, 28px, 400),
        $subtitle-2: mat.define-typography-level(14px, 22px, 500),
        $body-1: mat.define-typography-level(16px, 24px, 400),
        $body-2: mat.define-typography-level(14px, 20px, 400),
        $caption: mat.define-typography-level(12px, 20px, 400),
        $button: mat.define-typography-level(14px, 14px, 500),
        $overline:
          mat.define-typography-level(10px, 16px, 400, $letter-spacing: 0.1em),
      ),
    density: 0,
  )
);

// Include theme styles for core and each component used in your app
@include mat.all-component-themes($theme);

// Tailwind CSS
@tailwind base;
@tailwind components;
@tailwind utilities;

// Global styles
* {
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  margin: 0;
  font-family: "Roboto", "Helvetica Neue", sans-serif;
}

// Custom Material Design overrides
.mat-mdc-card {
  border-radius: 16px !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
}

.mat-mdc-button {
  border-radius: 20px !important;
}

.mat-mdc-fab {
  border-radius: 16px !important;
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  $dark-theme: mat.define-dark-theme(
    (
      color: (
        primary: $primary-palette,
        accent: $accent-palette,
        warn: $warn-palette,
      ),
      typography: mat.define-typography-config(),
      density: 0,
    )
  );

  @include mat.all-component-colors($dark-theme);
}

html, body { height: 100%; }
body { margin: 0; font-family: Roboto, "Helvetica Neue", sans-serif; }
